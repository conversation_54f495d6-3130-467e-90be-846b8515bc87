"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Image from "next/image"
import { useAuth } from "@/lib/auth-provider"
import { useCurrency } from "@/lib/currency-provider"
import { useProducts } from "@/lib/product-provider"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useToast } from "@/components/ui/use-toast"
import { Star, Eye, EyeOff, Upload, X, Plus, Image as ImageIcon } from "lucide-react"

interface ProductEditDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  product: any | null
}

export function ProductEditDialog({ open, onOpenChange, product }: ProductEditDialogProps) {
  const { currentLocation } = useAuth()
  const { formatCurrency } = useCurrency()
  const { toast } = useToast()
  const { updateProduct } = useProducts()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState({
    isActive: true,
    isFeatured: false,
    isNew: false,
    isBestSeller: false,
    isOnSale: false,
    retailPrice: "",
    salePrice: "",
    images: [] as string[],
  })

  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [imageUrl, setImageUrl] = useState("")

  useEffect(() => {
    if (product) {
      setFormData({
        isActive: product.isActive ?? true,
        isFeatured: product.isFeatured ?? false,
        isNew: product.isNew ?? false,
        isBestSeller: product.isBestSeller ?? false,
        isOnSale: product.isOnSale ?? false,
        retailPrice: product.price?.toString() || "",
        salePrice: product.salePrice?.toString() || "",
        images: product.images || [],
      })
      setImagePreview(product.images?.[0] || null)
      setImageUrl("")
    }
  }, [product])

  const handleChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  // Handle image upload
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          variant: "destructive",
          title: "Invalid file type",
          description: "Please select an image file.",
        })
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          variant: "destructive",
          title: "File too large",
          description: "Please select an image smaller than 5MB.",
        })
        return
      }

      // Create preview URL
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        setImagePreview(result)
        setFormData(prev => ({
          ...prev,
          images: [result, ...prev.images.slice(1)] // Replace first image or add as first
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  // Handle image URL input
  const handleImageUrlChange = (url: string) => {
    setImageUrl(url)
  }

  // Add image from URL
  const addImageFromUrl = () => {
    if (imageUrl.trim()) {
      setImagePreview(imageUrl)
      setFormData(prev => ({
        ...prev,
        images: [imageUrl, ...prev.images.slice(1)] // Replace first image or add as first
      }))
      setImageUrl("")
    }
  }

  // Remove image
  const handleRemoveImage = () => {
    setImagePreview(null)
    setFormData(prev => ({
      ...prev,
      images: prev.images.slice(1) // Remove first image
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Update the product using the product provider
      const updatedProduct = {
        ...product,
        isActive: formData.isActive,
        isFeatured: formData.isFeatured,
        isNew: formData.isNew,
        isBestSeller: formData.isBestSeller,
        isSale: formData.isOnSale,
        price: parseFloat(formData.retailPrice) || 0,
        salePrice: formData.salePrice ? parseFloat(formData.salePrice) : undefined,
        images: formData.images,
        image: formData.images[0] || product.image, // Update main image
        updatedAt: new Date(),
      }

      updateProduct(updatedProduct)

      toast({
        title: "Product updated successfully",
        description: `${product.name} has been updated and changes are reflected across all sections.`,
      })

      onOpenChange(false)
    } catch (error) {
      console.error("Failed to update product:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update product. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!product) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Edit Product</DialogTitle>
            <DialogDescription>
              Update product settings and e-commerce features for {product.name}.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Product Info Display */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                {product.images && product.images[0] && (
                  <img
                    src={product.images[0]}
                    alt={product.name}
                    className="w-12 h-12 object-cover rounded border"
                  />
                )}
                <div>
                  <h3 className="font-medium">{product.name}</h3>
                  <p className="text-sm text-gray-500">SKU: {product.sku}</p>
                  {product.rating && (
                    <div className="flex items-center gap-1 text-sm text-gray-500">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      {product.rating} ({product.reviewCount} reviews)
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Pricing */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="retailPrice">Retail Price</Label>
                <div className="relative">
                  <Input
                    id="retailPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.retailPrice}
                    onChange={(e) => handleChange("retailPrice", e.target.value)}
                    placeholder="0.00"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500">
                    {formData.retailPrice && <CurrencyDisplay amount={parseFloat(formData.retailPrice) || 0} />}
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="salePrice">Sale Price (Optional)</Label>
                <div className="relative">
                  <Input
                    id="salePrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.salePrice}
                    onChange={(e) => handleChange("salePrice", e.target.value)}
                    placeholder="0.00"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500">
                    {formData.salePrice && <CurrencyDisplay amount={parseFloat(formData.salePrice) || 0} />}
                  </div>
                </div>
              </div>
            </div>

            {/* Image Upload Section */}
            <div className="space-y-2">
              <Label>Product Image</Label>
              <div className="space-y-4">
                {/* Image Preview */}
                {imagePreview && (
                  <div className="relative w-full h-48 border rounded-lg overflow-hidden">
                    <Image
                      src={imagePreview}
                      alt="Product preview"
                      fill
                      className="object-cover"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2"
                      onClick={handleRemoveImage}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                )}

                {/* Upload Options */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* File Upload */}
                  <div>
                    <Label htmlFor="image-upload" className="cursor-pointer">
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
                        <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                        <p className="text-sm text-gray-600">Upload New Image</p>
                        <p className="text-xs text-gray-400">Max 5MB</p>
                      </div>
                    </Label>
                    <Input
                      id="image-upload"
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                  </div>

                  {/* URL Input */}
                  <div className="space-y-2">
                    <Label htmlFor="image-url">Or enter image URL</Label>
                    <div className="flex gap-2">
                      <Input
                        id="image-url"
                        type="url"
                        placeholder="https://example.com/image.jpg"
                        value={imageUrl}
                        onChange={(e) => handleImageUrlChange(e.target.value)}
                        className="flex-1"
                      />
                      <Button type="button" onClick={addImageFromUrl} size="sm">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Product Status */}
            <div className="space-y-4">
              <h4 className="font-medium">Product Status & Features</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => handleChange("isActive", checked)}
                  />
                  <Label htmlFor="isActive" className="flex items-center gap-2">
                    {formData.isActive ? (
                      <Eye className="h-4 w-4 text-green-600" />
                    ) : (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    )}
                    Visible in shop
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isFeatured"
                    checked={formData.isFeatured}
                    onCheckedChange={(checked) => handleChange("isFeatured", checked)}
                  />
                  <Label htmlFor="isFeatured">Featured product</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isNew"
                    checked={formData.isNew}
                    onCheckedChange={(checked) => handleChange("isNew", checked)}
                  />
                  <Label htmlFor="isNew">New product</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isBestSeller"
                    checked={formData.isBestSeller}
                    onCheckedChange={(checked) => handleChange("isBestSeller", checked)}
                  />
                  <Label htmlFor="isBestSeller">Best seller</Label>
                </div>
              </div>
            </div>

            {/* Current Features Preview */}
            <div className="space-y-2">
              <Label>Current Features</Label>
              <div className="flex flex-wrap gap-2">
                {formData.isFeatured && (
                  <Badge variant="default" className="text-xs">Featured</Badge>
                )}
                {formData.isNew && (
                  <Badge variant="secondary" className="text-xs">New</Badge>
                )}
                {formData.isBestSeller && (
                  <Badge variant="outline" className="text-xs">Best Seller</Badge>
                )}
                {formData.salePrice && parseFloat(formData.salePrice) > 0 && (
                  <Badge variant="destructive" className="text-xs">Sale</Badge>
                )}
                {!formData.isFeatured && !formData.isNew && !formData.isBestSeller && (!formData.salePrice || parseFloat(formData.salePrice) === 0) && (
                  <span className="text-sm text-gray-500">No special features</span>
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Updating..." : "Update Product"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
